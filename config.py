import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
ASSETS = os.getenv("ASSETS", "USDT,BTC,ETH,BNB").split(",")
FIAT = os.getenv("FIAT", "KES")
MAKER_FEE_RATE = float(os.getenv("MAKER_FEE_RATE", "0.002"))  # 0.2% fee
UPDATE_INTERVAL = int(os.getenv("UPDATE_INTERVAL", "30"))  # seconds
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

# API Endpoints
BINANCE_P2P_URL = "https://p2p.binance.com/bapi/c2c/v2/friendly/c2c/adv/search"
BINANCE_SPOT_URL = "https://api.binance.com/api/v3/ticker/price"

# Request settings
REQUEST_TIMEOUT = 10
MAX_RETRIES = 3
RETRY_DELAY = 1
