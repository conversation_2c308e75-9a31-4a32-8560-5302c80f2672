#!/usr/bin/env python3
"""
Final system test for Binance Arbitrage Analyzer
"""

print('Final System Test')
print('=' * 50)

# Test imports
try:
    import requests
    import config
    from binance_arbitrage import BinanceArbitrageAnalyzer
    print('✅ All imports successful')
except Exception as e:
    print(f'❌ Import error: {e}')
    exit(1)

# Test configuration
print(f'✅ Assets: {config.ASSETS}')
print(f'✅ Fiat: {config.FIAT}')
print(f'✅ Fee rate: {config.MAKER_FEE_RATE*100:.2f}%')

# Test analyzer
analyzer = BinanceArbitrageAnalyzer()
result = analyzer.calculate_arbitrage_opportunity('USDT')

if result['opportunities']:
    print('✅ Analyzer working correctly')
    print(f'✅ Found {len(result["opportunities"])} opportunities')
else:
    print('⚠️ No opportunities found (this is normal)')

print('🎉 All tests passed!')
