#!/usr/bin/env python3
"""
Original Binance P2P Arbitrage Script
Simple version of the arbitrage analyzer.
"""

import requests 
from datetime import datetime

# Configuration
ASSETS = ["USDT", "BTC"]
FIAT = "KES"
MAKER_FEE_RATE = 0.002  # 0.2% fee

def fetch_p2p_prices(asset, trade_type="BUY"):
    url = "https://p2p.binance.com/bapi/c2c/v2/friendly/c2c/adv/search"
    payload = {
        "asset": asset,
        "fiat": FIAT,
        "tradeType": trade_type,
        "page": 1,
        "rows": 5,
        "payTypes": [],
        "publisherType": None
    }
    headers = {"Content-Type": "application/json"}
    try:
        res = requests.post(url, json=payload, headers=headers, timeout=10)
        res.raise_for_status()
        data = res.json()
        offers = data.get("data", [])
        return [float(offer["adv"]["price"]) for offer in offers if "adv" in offer]
    except Exception as e:
        print(f"Error fetching P2P prices for {asset}: {e}")
        return []

def fetch_spot_price(asset, quote="USDT"):
    if asset == "USDT":
        return 1.0  # USDT/USDT is always 1
    symbol = f"{asset}{quote}"
    url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}"
    try:
        res = requests.get(url, timeout=10)
        res.raise_for_status()
        return float(res.json()["price"])
    except Exception as e:
        print(f"Error fetching spot price for {symbol}: {e}")
        return None

def analyze_arbitrage():
    print(f"\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔎 Arbitrage Opportunities in {FIAT}")

    for asset in ASSETS:
        print(f"\n🪙 Asset: {asset}")

        # P2P Prices
        p2p_buy_prices = fetch_p2p_prices(asset, "BUY")   # We buy from these users
        p2p_sell_prices = fetch_p2p_prices(asset, "SELL") # We sell to these users

        if not p2p_buy_prices or not p2p_sell_prices:
            print("⚠️ No P2P prices found.")
            continue

        best_p2p_buy = min(p2p_buy_prices) * (1 + MAKER_FEE_RATE)
        best_p2p_sell = max(p2p_sell_prices) * (1 - MAKER_FEE_RATE)

        # Spot Price in USDT
        spot_price_usdt = fetch_spot_price(asset)

        # P2P Arbitrage Spread (Buy low, sell high)
        p2p_spread = best_p2p_sell - best_p2p_buy
        print(f"  P2P Buy (after fee): {best_p2p_buy:.2f} {FIAT}")
        print(f"  P2P Sell (after fee): {best_p2p_sell:.2f} {FIAT}")
        print(f"  ➡️ P2P Spread: {p2p_spread:.2f} {FIAT}")

        # Spot Arbitrage - convert spot USDT to KES via P2P
        if asset != "USDT" and spot_price_usdt:
            usdt_sell_prices = fetch_p2p_prices("USDT", "SELL")
            if usdt_sell_prices:
                usdt_to_kes = max(usdt_sell_prices) * (1 - MAKER_FEE_RATE)
                spot_to_kes = spot_price_usdt * usdt_to_kes
                print(f"  Spot Price: {spot_price_usdt:.2f} USDT ≈ {spot_to_kes:.2f} {FIAT}")
                spot_arbitrage_spread = best_p2p_sell - spot_to_kes
                print(f"  ➡️ Spot to P2P Spread: {spot_arbitrage_spread:.2f} {FIAT}")

        # Simple Recommendation
        if p2p_spread > 0:
            print("✅ Arbitrage possible on P2P: Buy low, sell high.")
        if asset != "USDT" and spot_price_usdt and 'spot_arbitrage_spread' in locals() and spot_arbitrage_spread > 0:
            print("✅ Arbitrage possible via Spot: Buy on spot, sell on P2P.")
        if p2p_spread <= 0 and (asset == "USDT" or not spot_price_usdt or 'spot_arbitrage_spread' not in locals() or spot_arbitrage_spread <= 0):
            print("❌ No clear arbitrage at the moment.")

if __name__ == "__main__":
    # Run analysis
    analyze_arbitrage()
