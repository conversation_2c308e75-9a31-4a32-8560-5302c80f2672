@echo off
echo Binance P2P Arbitrage Analyzer
echo ================================
echo.
echo 1. Run enhanced analyzer (recommended)
echo 2. Run original simple script  
echo 3. Run test
echo 4. Exit
echo.

set /p choice="Select option (1-4): "

if "%choice%"=="1" (
    echo.
    echo Starting enhanced analyzer...
    python binance_arbitrage.py
) else if "%choice%"=="2" (
    echo.
    echo Starting original script...
    python original_script.py
) else if "%choice%"=="3" (
    echo.
    echo Running test...
    python test_analyzer.py
) else if "%choice%"=="4" (
    echo Goodbye!
    exit /b 0
) else (
    echo Invalid choice. Please select 1, 2, 3, or 4.
    pause
    goto :eof
)

pause
