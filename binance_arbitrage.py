#!/usr/bin/env python3
"""
Binance P2P Arbitrage Analyzer
Analyzes arbitrage opportunities between Binance P2P and Spot markets.
"""

import requests
import time
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from colorama import init, Fore, Style
from tabulate import tabulate
import config

# Initialize colorama for cross-platform colored output
init()

# Setup logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('arbitrage.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class BinanceArbitrageAnalyzer:
    """Analyzes arbitrage opportunities on Binance P2P and Spot markets."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/json'
        })
    
    def fetch_p2p_prices(self, asset: str, trade_type: str = "BUY", rows: int = 10) -> List[float]:
        """
        Fetch P2P prices for a given asset.
        
        Args:
            asset: The cryptocurrency asset (e.g., 'BTC', 'USDT')
            trade_type: 'BUY' or 'SELL'
            rows: Number of offers to fetch
            
        Returns:
            List of prices from P2P offers
        """
        payload = {
            "asset": asset,
            "fiat": config.FIAT,
            "tradeType": trade_type,
            "page": 1,
            "rows": rows,
            "payTypes": [],
            "publisherType": None
        }
        
        try:
            response = self.session.post(
                config.BINANCE_P2P_URL,
                json=payload,
                timeout=config.REQUEST_TIMEOUT
            )
            response.raise_for_status()
            data = response.json()
            
            if not data.get("success", True):
                logger.warning(f"P2P API returned error for {asset}: {data}")
                return []
            
            offers = data.get("data", [])
            prices = []
            
            for offer in offers:
                if "adv" in offer and "price" in offer["adv"]:
                    try:
                        price = float(offer["adv"]["price"])
                        prices.append(price)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Invalid price format in offer: {e}")
                        continue
            
            logger.debug(f"Fetched {len(prices)} {trade_type} prices for {asset}")
            return prices
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching P2P prices for {asset}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching P2P prices for {asset}: {e}")
            return []
    
    def fetch_spot_price(self, asset: str, quote: str = "USDT") -> Optional[float]:
        """
        Fetch spot price for a given asset.
        
        Args:
            asset: The cryptocurrency asset
            quote: The quote currency (default: USDT)
            
        Returns:
            Spot price or None if error
        """
        if asset == quote:
            return 1.0  # Same asset pair is always 1
        
        symbol = f"{asset}{quote}"
        
        try:
            response = self.session.get(
                f"{config.BINANCE_SPOT_URL}?symbol={symbol}",
                timeout=config.REQUEST_TIMEOUT
            )
            response.raise_for_status()
            data = response.json()
            
            price = float(data["price"])
            logger.debug(f"Fetched spot price for {symbol}: {price}")
            return price
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching spot price for {symbol}: {e}")
            return None
        except (KeyError, ValueError, TypeError) as e:
            logger.error(f"Invalid spot price data for {symbol}: {e}")
            return None
    
    def calculate_arbitrage_opportunity(self, asset: str) -> Dict:
        """
        Calculate arbitrage opportunities for a given asset.
        
        Args:
            asset: The cryptocurrency asset to analyze
            
        Returns:
            Dictionary containing arbitrage analysis results
        """
        result = {
            'asset': asset,
            'timestamp': datetime.now(),
            'p2p_buy_prices': [],
            'p2p_sell_prices': [],
            'spot_price_usdt': None,
            'opportunities': []
        }
        
        # Fetch P2P prices
        p2p_buy_prices = self.fetch_p2p_prices(asset, "BUY")
        p2p_sell_prices = self.fetch_p2p_prices(asset, "SELL")
        
        result['p2p_buy_prices'] = p2p_buy_prices
        result['p2p_sell_prices'] = p2p_sell_prices
        
        if not p2p_buy_prices or not p2p_sell_prices:
            logger.warning(f"Insufficient P2P data for {asset}")
            return result
        
        # Calculate best P2P prices after fees
        best_p2p_buy = min(p2p_buy_prices) * (1 + config.MAKER_FEE_RATE)
        best_p2p_sell = max(p2p_sell_prices) * (1 - config.MAKER_FEE_RATE)
        
        # P2P arbitrage opportunity
        p2p_spread = best_p2p_sell - best_p2p_buy
        p2p_spread_pct = (p2p_spread / best_p2p_buy) * 100 if best_p2p_buy > 0 else 0
        
        result['opportunities'].append({
            'type': 'P2P Arbitrage',
            'buy_price': best_p2p_buy,
            'sell_price': best_p2p_sell,
            'spread': p2p_spread,
            'spread_pct': p2p_spread_pct,
            'profitable': p2p_spread > 0
        })
        
        # Spot arbitrage (only for non-USDT assets)
        if asset != "USDT":
            spot_price_usdt = self.fetch_spot_price(asset)
            result['spot_price_usdt'] = spot_price_usdt
            
            if spot_price_usdt:
                # Get USDT to fiat rate
                usdt_sell_prices = self.fetch_p2p_prices("USDT", "SELL")
                if usdt_sell_prices:
                    usdt_to_fiat = max(usdt_sell_prices) * (1 - config.MAKER_FEE_RATE)
                    spot_to_fiat = spot_price_usdt * usdt_to_fiat
                    
                    spot_arbitrage_spread = best_p2p_sell - spot_to_fiat
                    spot_arbitrage_pct = (spot_arbitrage_spread / spot_to_fiat) * 100 if spot_to_fiat > 0 else 0
                    
                    result['opportunities'].append({
                        'type': 'Spot to P2P',
                        'buy_price': spot_to_fiat,
                        'sell_price': best_p2p_sell,
                        'spread': spot_arbitrage_spread,
                        'spread_pct': spot_arbitrage_pct,
                        'profitable': spot_arbitrage_spread > 0
                    })
        
        return result
    
    def print_analysis_results(self, results: List[Dict]):
        """Print formatted analysis results."""
        print(f"\n{Fore.CYAN}{'='*60}")
        print(f"📅 Binance Arbitrage Analysis - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔎 Analyzing opportunities in {config.FIAT}")
        print(f"{'='*60}{Style.RESET_ALL}\n")
        
        for result in results:
            asset = result['asset']
            print(f"{Fore.YELLOW}🪙 Asset: {asset}{Style.RESET_ALL}")
            
            if not result['opportunities']:
                print(f"  {Fore.RED}⚠️ No data available{Style.RESET_ALL}\n")
                continue
            
            # Create table for opportunities
            table_data = []
            for opp in result['opportunities']:
                color = Fore.GREEN if opp['profitable'] else Fore.RED
                status = "✅ Profitable" if opp['profitable'] else "❌ Not Profitable"
                
                table_data.append([
                    opp['type'],
                    f"{opp['buy_price']:.2f} {config.FIAT}",
                    f"{opp['sell_price']:.2f} {config.FIAT}",
                    f"{color}{opp['spread']:.2f} {config.FIAT}{Style.RESET_ALL}",
                    f"{color}{opp['spread_pct']:.2f}%{Style.RESET_ALL}",
                    f"{color}{status}{Style.RESET_ALL}"
                ])
            
            headers = ["Strategy", "Buy Price", "Sell Price", "Spread", "Spread %", "Status"]
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
            print()
    
    def run_continuous_analysis(self):
        """Run continuous arbitrage analysis."""
        print(f"{Fore.GREEN}🚀 Starting Binance Arbitrage Analyzer{Style.RESET_ALL}")
        print(f"Assets: {', '.join(config.ASSETS)}")
        print(f"Fiat: {config.FIAT}")
        print(f"Update interval: {config.UPDATE_INTERVAL} seconds")
        print(f"Fee rate: {config.MAKER_FEE_RATE*100:.2f}%\n")
        
        try:
            while True:
                results = []
                for asset in config.ASSETS:
                    result = self.calculate_arbitrage_opportunity(asset)
                    results.append(result)
                
                self.print_analysis_results(results)
                
                print(f"{Fore.BLUE}⏳ Waiting {config.UPDATE_INTERVAL} seconds for next update...{Style.RESET_ALL}")
                time.sleep(config.UPDATE_INTERVAL)
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Analysis stopped by user{Style.RESET_ALL}")
        except Exception as e:
            logger.error(f"Unexpected error in continuous analysis: {e}")
            print(f"{Fore.RED}❌ Error occurred: {e}{Style.RESET_ALL}")


def main():
    """Main function to run the arbitrage analyzer."""
    analyzer = BinanceArbitrageAnalyzer()
    analyzer.run_continuous_analysis()


if __name__ == "__main__":
    main()
