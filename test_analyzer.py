#!/usr/bin/env python3
"""
Test script for the Binance Arbitrage Analyzer
"""

from binance_arbitrage import BinanceArbitrageAnalyzer

def test_analyzer():
    print("🧪 Testing Binance Arbitrage Analyzer...")
    
    analyzer = BinanceArbitrageAnalyzer()
    
    # Test with a single asset
    print("\n📊 Testing with USDT...")
    result = analyzer.calculate_arbitrage_opportunity("USDT")
    
    if result['opportunities']:
        print("✅ Analysis completed successfully!")
        analyzer.print_analysis_results([result])
    else:
        print("⚠️ No opportunities found or data unavailable")
    
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    test_analyzer()
