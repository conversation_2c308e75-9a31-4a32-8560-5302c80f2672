#!/usr/bin/env python3
"""
Simple runner script for the Binance Arbitrage Analyzer
"""

import sys
import os

def main():
    print("Binance P2P Arbitrage Analyzer")
    print("=" * 40)
    print("1. Run enhanced analyzer (recommended)")
    print("2. Run original simple script")
    print("3. Exit")
    
    while True:
        try:
            choice = input("\nSelect option (1-3): ").strip()
            
            if choice == "1":
                print("\n🚀 Starting enhanced analyzer...")
                from binance_arbitrage import main as enhanced_main
                enhanced_main()
                break
            elif choice == "2":
                print("\n🚀 Starting original script...")
                from original_script import analyze_arbitrage
                analyze_arbitrage()
                break
            elif choice == "3":
                print("👋 Goodbye!")
                sys.exit(0)
            else:
                print("❌ Invalid choice. Please select 1, 2, or 3.")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Error: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
