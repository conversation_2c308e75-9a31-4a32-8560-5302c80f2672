# Binance P2P Arbitrage Analyzer

A Python script that analyzes arbitrage opportunities between Binance P2P and Spot markets.

## Features

- **Real-time Analysis**: Continuously monitors arbitrage opportunities
- **Multiple Assets**: Supports multiple cryptocurrencies (USDT, BTC, ETH, BNB, etc.)
- **P2P Arbitrage**: Identifies buy-low-sell-high opportunities within P2P market
- **Spot-to-P2P Arbitrage**: Compares spot prices with P2P prices
- **Fee Calculation**: Includes maker fees in profit calculations
- **Colored Output**: Easy-to-read colored terminal output
- **Logging**: Comprehensive logging for debugging and monitoring
- **Error Handling**: Robust error handling and retry mechanisms

## Installation

1. **Clone or download** this repository
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Configure environment** (optional):
   ```bash
   cp .env.example .env
   # Edit .env file with your preferred settings
   ```

## Configuration

Edit the `.env` file or modify `config.py` to customize:

- `ASSETS`: Comma-separated list of assets to analyze (default: USDT,BTC,ETH,BNB)
- `FIAT`: Fiat currency for P2P trading (default: KES)
- `MAKER_FEE_RATE`: Trading fee rate (default: 0.002 = 0.2%)
- `UPDATE_INTERVAL`: Seconds between updates (default: 30)
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)

## Usage

### Run the analyzer:
```bash
python binance_arbitrage.py
```

### Single analysis (original script):
```bash
python original_script.py
```

## How It Works

### P2P Arbitrage
1. Fetches buy and sell prices from Binance P2P market
2. Calculates best buy price (lowest) and best sell price (highest)
3. Applies maker fees to both sides
4. Determines if profit is possible by buying low and selling high

### Spot-to-P2P Arbitrage
1. Fetches spot price from Binance API
2. Converts spot price to local fiat using USDT P2P rates
3. Compares with P2P sell prices
4. Identifies opportunities to buy on spot and sell on P2P

## Output

The script displays:
- **Asset**: Cryptocurrency being analyzed
- **Strategy**: Type of arbitrage (P2P or Spot-to-P2P)
- **Buy/Sell Prices**: After-fee prices
- **Spread**: Profit potential in fiat currency
- **Spread %**: Percentage profit
- **Status**: Whether the opportunity is profitable

## Risk Disclaimer

⚠️ **Important**: This tool is for educational and analysis purposes only.

- Arbitrage trading involves significant risks
- Market conditions change rapidly
- Always verify opportunities manually before trading
- Consider slippage, execution time, and market volatility
- Past performance doesn't guarantee future results

## Troubleshooting

### Common Issues:

1. **Network Errors**: Check internet connection and API availability
2. **Rate Limiting**: Reduce update frequency if getting rate limited
3. **Invalid Symbols**: Ensure asset symbols are correct for Binance
4. **No Data**: Some assets may not have active P2P markets

### Logs:
Check `arbitrage.log` for detailed error information.

## License

This project is for educational purposes. Use at your own risk.
